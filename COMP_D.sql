-- Active: 1757514664355@@127.0.0.1@3306@comp_d
/*
<PERSON><PERSON><PERSON>
0402425
COMP D - Part 1 & Part 2

This script creates a MySQL database named 'comp_d', creates a
'people' table, loads data from a CSV file, and completes Part 2
with a custom table and queries.
*/

-- ============================================================================
-- PART 1: Database Setup and Data Import
-- ============================================================================

-- Drop the database if it already exists
DROP DATABASE IF EXISTS comp_d;

-- Create the new database
CREATE DATABASE comp_d;

-- Select the database for use
USE comp_d;

-- Create the people table with proper constraints
CREATE TABLE people (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL DEFAULT ""
);

-- Enable local_infile for data loading (required for LOAD DATA LOCAL INFILE)
SET GLOBAL local_infile = 1;

-- Load data from CSV file using absolute path
-- Note: Using REPLACE to handle any duplicate entries gracefully
LOAD DATA LOCAL INFILE 'C:/Comp d/modual D(Sheet1).csv'
INTO TABLE people
FIELDS TERMINATED BY '\n'  -- Each line is a complete name
LINES TERMINATED BY '\n'
(full_name);

-- Verify data was loaded successfully
SELECT COUNT(*) AS 'Total Records Loaded' FROM people;

-- Display the first 50 rows as required by assignment (Part 1, Step 10)
SELECT full_name FROM people LIMIT 50;

-- ============================================================================
-- PART 2: Custom Table Creation and Queries
-- ============================================================================

/*
Creating a 'books' table to manage a library inventory system.
This table will store information about books including title, author,
publication year, genre, and availability status.
*/

-- Create the books table with appropriate constraints
CREATE TABLE books (
    id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    author VARCHAR(150) NOT NULL,
    publication_year INT NOT NULL,
    genre VARCHAR(50) NOT NULL DEFAULT 'Fiction',
    price DECIMAL(8,2) NOT NULL,
    is_available BOOLEAN NOT NULL DEFAULT TRUE
);

-- Insert 5 unique records using a single INSERT statement
INSERT INTO books (title, author, publication_year, genre, price, is_available) VALUES
('The Great Gatsby', 'F. Scott Fitzgerald', 1925, 'Classic Literature', 12.99, TRUE),
('To Kill a Mockingbird', 'Harper Lee', 1960, 'Classic Literature', 14.50, TRUE),
('1984', 'George Orwell', 1949, 'Dystopian Fiction', 13.25, FALSE),
('Pride and Prejudice', 'Jane Austen', 1813, 'Romance', 11.75, TRUE),
('The Catcher in the Rye', 'J.D. Salinger', 1951, 'Coming of Age', 15.00, TRUE);

-- ============================================================================
-- 10 SELECT Statements with WHERE and ORDER BY clauses
-- ============================================================================

-- Query 1: Find all available books ordered by title
-- Uses comparison operator (=) and ORDER BY
SELECT * FROM books
WHERE is_available = TRUE
ORDER BY title ASC;

-- Query 2: Find books published after 1950 ordered by publication year
-- Uses comparison operator (>) and ORDER BY
SELECT title, author, publication_year FROM books
WHERE publication_year > 1950
ORDER BY publication_year DESC;

-- Query 3: Find books with specific genres using IN statement
-- Uses IN operator and ORDER BY
SELECT title, author, genre FROM books
WHERE genre IN ('Classic Literature', 'Romance')
ORDER BY author ASC;

-- Query 4: Find books with titles containing specific words using LIKE
-- Uses LIKE operator and ORDER BY
SELECT title, author, price FROM books
WHERE title LIKE '%The%'
ORDER BY price DESC;

-- Query 5: Find expensive available books using logical operators
-- Uses AND logical operator and ORDER BY
SELECT title, author, price FROM books
WHERE price > 13.00 AND is_available = TRUE
ORDER BY price ASC;

-- Query 6: Find books by publication era or genre using OR
-- Uses OR logical operator and ORDER BY
SELECT title, author, publication_year, genre FROM books
WHERE publication_year < 1900 OR genre = 'Dystopian Fiction'
ORDER BY publication_year ASC;

-- Query 7: Find books with authors whose names start with specific letters
-- Uses LIKE with wildcard and ORDER BY
SELECT title, author FROM books
WHERE author LIKE 'J%'
ORDER BY author ASC;

-- Query 8: Find books within a specific price range
-- Uses comparison operators (BETWEEN equivalent) and ORDER BY
SELECT title, price, is_available FROM books
WHERE price >= 12.00 AND price <= 14.00
ORDER BY price DESC;

-- Query 9: Find unavailable books with specific characteristics
-- Uses comparison and logical operators with ORDER BY
SELECT title, author, genre FROM books
WHERE is_available = FALSE AND publication_year > 1940
ORDER BY title ASC;

-- Query 10: Find books published in specific decades
-- Uses IN with calculated values and ORDER BY
SELECT title, author, publication_year FROM books
WHERE FLOOR(publication_year/10)*10 IN (1940, 1950, 1960)
ORDER BY publication_year ASC, title ASC;

-- ============================================================================
-- Table Modification: Add New Column
-- ============================================================================

-- Add a new column for ISBN using ALTER TABLE
ALTER TABLE books
ADD COLUMN isbn VARCHAR(20) DEFAULT NULL;

-- ============================================================================
-- Update Record: Add Data to New Column
-- ============================================================================

-- Update one record to add ISBN information
UPDATE books
SET isbn = '978-0-7432-7356-5'
WHERE title = 'The Great Gatsby';

-- ============================================================================
-- Final Verification Queries
-- ============================================================================

-- Show the updated table structure
DESCRIBE books;

-- Show all records with the new ISBN column
SELECT * FROM books ORDER BY id;

-- Show final count of people records
SELECT COUNT(*) AS 'Final People Count' FROM people;
